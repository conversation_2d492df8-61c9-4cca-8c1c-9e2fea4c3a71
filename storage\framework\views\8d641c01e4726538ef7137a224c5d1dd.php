<?php
    $total = $paginator->lastPage();
    $current = $paginator->currentPage();
?>

<ul class="pagination my-pagination">
    
    <?php if($total == 1): ?>
        <li class="page-item disabled"><span class="page-link">&laquo;&laquo;</span></li>
        <li class="page-item disabled"><span class="page-link">&laquo;</span></li>
        <li class="page-item disabled"><span class="page-link">1</span></li>
        <li class="page-item disabled"><span class="page-link">&raquo;</span></li>
        <li class="page-item disabled"><span class="page-link">&raquo;&raquo;</span></li>
    <?php else: ?>
        
        <li class="page-item <?php echo e($current == 1 ? 'disabled' : ''); ?>">
            <a class="page-link" href="<?php echo e($paginator->url(1)); ?>" tabindex="-1">&laquo;&laquo;</a>
        </li>
        
        <li class="page-item <?php echo e($current == 1 ? 'disabled' : ''); ?>">
            <a class="page-link" href="<?php echo e($paginator->previousPageUrl()); ?>" tabindex="-1">&laquo;</a>
        </li>

        
        <?php if($total <= 4): ?>
            <?php for($i = 1; $i <= $total; $i++): ?>
                <li class="page-item <?php echo e($current == $i ? 'active' : ''); ?>">
                    <a class="page-link" href="<?php echo e($paginator->url($i)); ?>"><?php echo e($i); ?></a>
                </li>
            <?php endfor; ?>
        <?php else: ?>
            
            <?php if($current <= 2): ?>
                
                <?php for($i = 1; $i <= 2; $i++): ?>
                    <li class="page-item <?php echo e($current == $i ? 'active' : ''); ?>">
                        <a class="page-link" href="<?php echo e($paginator->url($i)); ?>"><?php echo e($i); ?></a>
                    </li>
                <?php endfor; ?>
                <li style="margin-top: 8%;" class="page-item disabled"><span class="page-link">...</span></li>
                <li class="page-item"><a class="page-link" href="<?php echo e($paginator->url($total - 1)); ?>"><?php echo e($total - 1); ?></a></li>
                <li class="page-item"><a class="page-link" href="<?php echo e($paginator->url($total)); ?>"><?php echo e($total); ?></a></li>
            <?php elseif($current >= $total - 1): ?>
                
                <li class="page-item"><a class="page-link" href="<?php echo e($paginator->url(1)); ?>">1</a></li>
                <li class="page-item"><a class="page-link" href="<?php echo e($paginator->url(2)); ?>">2</a></li>
                <li style="margin-top: 8%;" class="page-item disabled"><span class="page-link">...</span></li>
                <?php for($i = $total - 1; $i <= $total; $i++): ?>
                    <li class="page-item <?php echo e($current == $i ? 'active' : ''); ?>">
                        <a class="page-link" href="<?php echo e($paginator->url($i)); ?>"><?php echo e($i); ?></a>
                    </li>
                <?php endfor; ?>
            <?php else: ?>
                
                <li class="page-item"><a class="page-link" href="<?php echo e($paginator->url($current - 1)); ?>"><?php echo e($current - 1); ?></a></li>
                <li class="page-item active"><a class="page-link" href="<?php echo e($paginator->url($current)); ?>"><?php echo e($current); ?></a></li>
                <li style="margin-top: 8%;" class="page-item disabled"><span class="page-link">...</span></li>
                <li class="page-item"><a class="page-link" href="<?php echo e($paginator->url($total - 1)); ?>"><?php echo e($total - 1); ?></a></li>
                <li class="page-item"><a class="page-link" href="<?php echo e($paginator->url($total)); ?>"><?php echo e($total); ?></a></li>
            <?php endif; ?>
        <?php endif; ?>

        
        <li class="page-item <?php echo e($current == $total ? 'disabled' : ''); ?>">
            <a class="page-link" href="<?php echo e($paginator->nextPageUrl()); ?>">&raquo;</a>
        </li>
        
        <li class="page-item <?php echo e($current == $total ? 'disabled' : ''); ?>">
            <a class="page-link" href="<?php echo e($paginator->url($total)); ?>">&raquo;&raquo;</a>
        </li>
    <?php endif; ?>
</ul><?php /**PATH /var/www/html/resources/views/vendor/pagination/custom.blade.php ENDPATH**/ ?>