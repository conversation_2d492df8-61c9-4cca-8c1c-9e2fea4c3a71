<?php if($paginator->hasPages()): ?>
    <ul class="pagination my-pagination">
        
        <li class="page-item <?php echo e($paginator->onFirstPage() ? 'disabled' : ''); ?>">
            <a class="page-link" href="<?php echo e($paginator->url(1)); ?>" tabindex="-1">&laquo;&laquo;</a>
        </li>

        
        <li class="page-item <?php echo e($paginator->onFirstPage() ? 'disabled' : ''); ?>">
            <a class="page-link" href="<?php echo e($paginator->previousPageUrl()); ?>" tabindex="-1">&laquo;</a>
        </li>

        <?php
            $total = $paginator->lastPage();
            $current = $paginator->currentPage();
        ?>

        
        <?php if($current <= 2): ?>
            <?php for($i = 1; $i <= $current; $i++): ?>
                <li class="page-item <?php echo e($current == $i ? 'active' : ''); ?>">
                    <a class="page-link" href="<?php echo e($paginator->url($i)); ?>"><?php echo e($i); ?></a>
                </li>
            <?php endfor; ?>
            <li class="page-item disabled"><span class="page-link">...</span></li>
            <li class="page-item"><a class="page-link" href="<?php echo e($paginator->url($total - 1)); ?>"><?php echo e($total - 1); ?></a></li>
            <li class="page-item"><a class="page-link" href="<?php echo e($paginator->url($total)); ?>"><?php echo e($total); ?></a></li>
        
        <?php elseif($current >= $total - 1): ?>
            <li class="page-item"><a class="page-link" href="<?php echo e($paginator->url(1)); ?>">1</a></li>
            <li class="page-item"><a class="page-link" href="<?php echo e($paginator->url(2)); ?>">2</a></li>
            <li class="page-item disabled"><span class="page-link">...</span></li>
            <?php for($i = $total - 1; $i <= $total; $i++): ?>
                <li class="page-item <?php echo e($current == $i ? 'active' : ''); ?>">
                    <a class="page-link" href="<?php echo e($paginator->url($i)); ?>"><?php echo e($i); ?></a>
                </li>
            <?php endfor; ?>
        
        <?php else: ?>
            <li class="page-item"><a class="page-link" href="<?php echo e($paginator->url($current - 1)); ?>"><?php echo e($current - 1); ?></a></li>
            <li class="page-item active"><a class="page-link" href="<?php echo e($paginator->url($current)); ?>"><?php echo e($current); ?></a></li>
            <li class="page-item disabled"><span class="page-link">...</span></li>
            <li class="page-item"><a class="page-link" href="<?php echo e($paginator->url($total - 1)); ?>"><?php echo e($total - 1); ?></a></li>
            <li class="page-item"><a class="page-link" href="<?php echo e($paginator->url($total)); ?>"><?php echo e($total); ?></a></li>
        <?php endif; ?>

        
        <li class="page-item <?php echo e(!$paginator->hasMorePages() ? 'disabled' : ''); ?>">
            <a class="page-link" href="<?php echo e($paginator->nextPageUrl()); ?>">&raquo;</a>
        </li>

        
        <li class="page-item <?php echo e($paginator->currentPage() == $paginator->lastPage() ? 'disabled' : ''); ?>">
            <a class="page-link" href="<?php echo e($paginator->url($paginator->lastPage())); ?>">&raquo;&raquo;</a>
        </li>
    </ul>
<?php endif; ?><?php /**PATH /var/www/html/resources/views/vendor/pagination/bootstrap-4.blade.php ENDPATH**/ ?>