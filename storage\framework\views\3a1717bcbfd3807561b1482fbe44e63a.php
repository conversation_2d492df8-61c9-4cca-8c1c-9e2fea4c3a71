<?php if (isset($component)) { $__componentOriginal8001c520f4b7dcb40a16cd3b411856d1 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.layouts.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin::layouts'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('title', null, []); ?> 
        Quản lý tin tức
     <?php $__env->endSlot(); ?>
    <div class="flex gap-4 justify-between items-center max-sm:flex-wrap">
        <p class="text-xl text-gray-800 dark:text-white font-bold">
            Quản lý tin tức
        </p>

        <div class="flex gap-x-2.5 items-center">
            <a
                href="<?php echo e(route('admin.news.create')); ?>"
                class="primary-button"
            >
                Thêm tin tức
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-gray-900 rounded box-shadow mt-4">
        <div class="p-4 border-b dark:border-gray-800">
            <form method="GET" action="<?php echo e(route('admin.news.index')); ?>" class="flex gap-4 items-end">
                <div class="flex-1">
                    <label class="text-gray-800 dark:text-white font-medium">Tìm kiếm</label>
                    <input
                        type="text"
                        name="search"
                        value="<?php echo e(request('search')); ?>"
                        placeholder="Tìm theo tiêu đề..."
                        class="flex w-full min-h-[39px] py-2 px-3 border rounded-[6px] text-[14px] text-gray-600 dark:text-gray-300 transition-all hover:border-gray-400 dark:hover:border-gray-400 focus:border-gray-400 dark:focus:border-gray-400 dark:bg-gray-900 dark:border-gray-800"
                    />
                </div>

                <div>
                    <label class="text-gray-800 dark:text-white font-medium">Danh mục</label>
                    <select
                        name="category_id"
                        class="flex w-full min-h-[39px] py-2 px-3 border rounded-[6px] text-[14px] text-gray-600 dark:text-gray-300 transition-all hover:border-gray-400 dark:hover:border-gray-400 focus:border-gray-400 dark:focus:border-gray-400 dark:bg-gray-900 dark:border-gray-800"
                    >
                        <option value="">Tất cả danh mục</option>
                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($category->id); ?>" <?php echo e(request('category_id') == $category->id ? 'selected' : ''); ?>>
                                <?php echo e($category->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <div>
                    <label class="text-gray-800 dark:text-white font-medium">Trạng thái</label>
                    <select
                        name="status"
                        class="flex w-full min-h-[39px] py-2 px-3 border rounded-[6px] text-[14px] text-gray-600 dark:text-gray-300 transition-all hover:border-gray-400 dark:hover:border-gray-400 focus:border-gray-400 dark:focus:border-gray-400 dark:bg-gray-900 dark:border-gray-800"
                    >
                        <option value="">Tất cả trạng thái</option>
                        <option value="published" <?php echo e(request('status') == 'published' ? 'selected' : ''); ?>>Đã xuất bản</option>
                        <option value="draft" <?php echo e(request('status') == 'draft' ? 'selected' : ''); ?>>Bản nháp</option>
                    </select>
                </div>

                <button type="submit" class="primary-button">Lọc</button>
                <a href="<?php echo e(route('admin.news.index')); ?>" class="secondary-button">Xóa bộ lọc</a>
            </form>
        </div>

        <div class="table-responsive grid w-full">
            <table class="table">
                <thead class="text-sm text-black dark:text-white">
                    <tr>
                        <th class="!p-4">ID</th>
                        <th class="!p-4">Tiêu đề</th>
                        <th class="!p-4">Danh mục</th>
                        <th class="!p-4">Trạng thái</th>
                        <th class="!p-4">Nổi bật</th>
                        <th class="!p-4">Ngày xuất bản</th>
                        <th class="!p-4">Hành động</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $news; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-950">
                            <td class="!p-4"><?php echo e($item->id); ?></td>
                            <td class="!p-4">
                                <div class="flex items-center gap-2">
                                    <?php if($item->featured_image): ?>
                                        <img src="<?php echo e($item->featured_image_url); ?>" alt="" class="w-10 h-10 rounded object-cover">
                                    <?php endif; ?>
                                    <div>
                                        <div class="font-medium"><?php echo e(Str::limit($item->title, 50)); ?></div>
                                        <div class="text-sm text-gray-500"><?php echo e($item->slug); ?></div>
                                    </div>
                                </div>
                            </td>
                            <td class="!p-4">
                                <?php echo e($item->category ? $item->category->name : 'Chưa phân loại'); ?>

                            </td>
                            <td class="!p-4">
                                <span class="badge <?php echo e($item->status == 'published' ? 'badge-success' : 'badge-warning'); ?>">
                                    <?php echo e($item->status == 'published' ? 'Đã xuất bản' : 'Bản nháp'); ?>

                                </span>
                            </td>
                            <td class="!p-4">
                                <?php if($item->is_featured): ?>
                                    <span class="badge badge-info">Nổi bật</span>
                                <?php endif; ?>
                            </td>
                            <td class="!p-4">
                                <?php echo e($item->published_at ? $item->published_at->format('d/m/Y H:i') : '-'); ?>

                            </td>
                            <td class="!p-4">
                                <div class="flex gap-1.5">
                                    <a
                                        href="<?php echo e(route('admin.news.edit', $item)); ?>"
                                        class="icon-edit text-2xl cursor-pointer hover:rounded-md p-1.5 hover:bg-gray-200 dark:hover:bg-gray-800"
                                    ></a>

                                    <form
                                        method="POST"
                                        action="<?php echo e(route('admin.news.destroy', $item)); ?>"
                                        onsubmit="return confirm('Bạn có chắc chắn muốn xóa tin tức này?')"
                                    >
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>

                                        <button
                                            type="submit"
                                            class="icon-delete text-2xl cursor-pointer hover:rounded-md p-1.5 hover:bg-gray-200 dark:hover:bg-gray-800"
                                        ></button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="7" class="!p-4 text-center">
                                Chưa có tin tức nào
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <div class="p-4">
            <?php echo e($news->appends(request()->query())->links()); ?>

        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1)): ?>
<?php $attributes = $__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1; ?>
<?php unset($__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8001c520f4b7dcb40a16cd3b411856d1)): ?>
<?php $component = $__componentOriginal8001c520f4b7dcb40a16cd3b411856d1; ?>
<?php unset($__componentOriginal8001c520f4b7dcb40a16cd3b411856d1); ?>
<?php endif; ?>
<?php /**PATH /var/www/html/resources/views/admin/news/index.blade.php ENDPATH**/ ?>