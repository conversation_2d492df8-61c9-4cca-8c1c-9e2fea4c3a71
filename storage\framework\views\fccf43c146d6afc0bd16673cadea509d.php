<?php if (isset($component)) { $__componentOriginal8001c520f4b7dcb40a16cd3b411856d1 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.layouts.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin::layouts'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('title', null, []); ?> 
        Danh mục tin tức
     <?php $__env->endSlot(); ?>
    <div class="flex gap-4 justify-between items-center max-sm:flex-wrap">
        <p class="text-xl text-gray-800 dark:text-white font-bold">
            Danh mục tin tức
        </p>

        <div class="flex gap-x-2.5 items-center">
            <a
                href="<?php echo e(route('admin.news-categories.create')); ?>"
                class="primary-button"
            >
                Thêm danh mục
            </a>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-900 rounded box-shadow mt-4">
        <div class="table-responsive grid w-full">
            <table class="table">
                <thead class="text-sm text-black dark:text-white">
                    <tr>
                        <th class="!p-4">ID</th>
                        <th class="!p-4">Tên danh mục</th>
                        <th class="!p-4">Slug</th>
                        <th class="!p-4">Trạng thái</th>
                        <th class="!p-4">Thứ tự</th>
                        <th class="!p-4">Hành động</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-950">
                            <td class="!p-4"><?php echo e($category->id); ?></td>
                            <td class="!p-4"><?php echo e($category->name); ?></td>
                            <td class="!p-4"><?php echo e($category->slug); ?></td>
                            <td class="!p-4">
                                <span class="badge <?php echo e($category->status ? 'badge-success' : 'badge-danger'); ?>">
                                    <?php echo e($category->status ? 'Hoạt động' : 'Không hoạt động'); ?>

                                </span>
                            </td>
                            <td class="!p-4"><?php echo e($category->sort_order); ?></td>
                            <td class="!p-4">
                                <div class="flex gap-1.5">
                                    <a
                                        href="<?php echo e(route('admin.news-categories.edit', $category)); ?>"
                                        class="icon-edit text-2xl cursor-pointer hover:rounded-md p-1.5 hover:bg-gray-200 dark:hover:bg-gray-800"
                                    ></a>

                                    <form
                                        method="POST"
                                        action="<?php echo e(route('admin.news-categories.destroy', $category)); ?>"
                                        onsubmit="return confirm('Bạn có chắc chắn muốn xóa danh mục này?')"
                                    >
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>

                                        <button
                                            type="submit"
                                            class="icon-delete text-2xl cursor-pointer hover:rounded-md p-1.5 hover:bg-gray-200 dark:hover:bg-gray-800"
                                        ></button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="6" class="!p-4 text-center">
                                Chưa có danh mục nào
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <div class="p-4">
            <?php echo e($categories->links()); ?>

        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1)): ?>
<?php $attributes = $__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1; ?>
<?php unset($__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8001c520f4b7dcb40a16cd3b411856d1)): ?>
<?php $component = $__componentOriginal8001c520f4b7dcb40a16cd3b411856d1; ?>
<?php unset($__componentOriginal8001c520f4b7dcb40a16cd3b411856d1); ?>
<?php endif; ?>
<?php /**PATH /var/www/html/resources/views/admin/news-categories/index.blade.php ENDPATH**/ ?>