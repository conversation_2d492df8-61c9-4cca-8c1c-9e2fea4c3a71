<?php $__env->startSection('page_title'); ?>
    Thêm hoạt chất mới
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="content">
        <form method="POST" action="<?php echo e(route('admin.catalog.active-ingredients.store')); ?>" @submit.prevent="onSubmit">
            <?php echo csrf_field(); ?>

            <div class="page-header">
                <div class="page-title">
                    <h1>Thêm hoạt chất mới</h1>
                </div>
                <div class="page-action">
                    <button type="submit" class="btn btn-lg btn-primary">
                        L<PERSON>u hoạt chất
                    </button>
                </div>
            </div>

            <div class="page-content">
                <?php echo $__env->make('admin::layouts.flash-messages', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                <div class="form-container">
                    <div class="control-group" :class="[errors.has('name') ? 'has-error' : '']">
                        <label for="name" class="required">Tên hoạt chất</label>
                        <input type="text" 
                               class="control" 
                               id="name" 
                               name="name" 
                               value="<?php echo e(old('name')); ?>" 
                               v-validate="'required'"
                               data-vv-as="&quot;Tên hoạt chất&quot;">
                        <span class="control-error" v-if="errors.has('name')">{{ errors.first('name') }}</span>
                    </div>

                    <div class="control-group">
                        <label for="description">Mô tả</label>
                        <textarea class="control" id="description" name="description"><?php echo e(old('description')); ?></textarea>
                    </div>

                    <div class="control-group">
                        <label for="status">Trạng thái</label>
                        <select class="control" id="status" name="status">
                            <option value="1" <?php echo e(old('status', 1) == 1 ? 'selected' : ''); ?>>Hoạt động</option>
                            <option value="0" <?php echo e(old('status') == 0 ? 'selected' : ''); ?>>Không hoạt động</option>
                        </select>
                    </div>
                </div>
            </div>
        </form>
    </div>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('admin::layouts.content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/html/resources/views/admin/catalog/active-ingredients/create.blade.php ENDPATH**/ ?>