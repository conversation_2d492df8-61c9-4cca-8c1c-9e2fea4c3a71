<div class="mt-3.5 flex gap-2.5 max-xl:flex-wrap">
    <!-- Left Container -->
    <div class="flex flex-1 flex-col gap-2 overflow-y-auto max-xl:flex-auto">
        <!-- Cart Items Shimmer Effect -->
        <?php if (isset($component)) { $__componentOriginal3443e414903d23f77d083031e17dbeef = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3443e414903d23f77d083031e17dbeef = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.shimmer.sales.orders.create.cart.items','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin::shimmer.sales.orders.create.cart.items'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3443e414903d23f77d083031e17dbeef)): ?>
<?php $attributes = $__attributesOriginal3443e414903d23f77d083031e17dbeef; ?>
<?php unset($__attributesOriginal3443e414903d23f77d083031e17dbeef); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3443e414903d23f77d083031e17dbeef)): ?>
<?php $component = $__componentOriginal3443e414903d23f77d083031e17dbeef; ?>
<?php unset($__componentOriginal3443e414903d23f77d083031e17dbeef); ?>
<?php endif; ?>
    </div>

    <!-- Right Container -->
    <div class="flex w-[360px] max-w-full flex-col gap-2 max-sm:w-full">
        <!-- Cart Items Shimmer Effect -->
        <?php if (isset($component)) { $__componentOriginal2ad546b86dcbe56ffecded973142dc8e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2ad546b86dcbe56ffecded973142dc8e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.shimmer.sales.orders.create.items','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin::shimmer.sales.orders.create.items'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2ad546b86dcbe56ffecded973142dc8e)): ?>
<?php $attributes = $__attributesOriginal2ad546b86dcbe56ffecded973142dc8e; ?>
<?php unset($__attributesOriginal2ad546b86dcbe56ffecded973142dc8e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2ad546b86dcbe56ffecded973142dc8e)): ?>
<?php $component = $__componentOriginal2ad546b86dcbe56ffecded973142dc8e; ?>
<?php unset($__componentOriginal2ad546b86dcbe56ffecded973142dc8e); ?>
<?php endif; ?>

        <!-- Wishlist Items Shimmer Effect -->
        <?php if (isset($component)) { $__componentOriginal2ad546b86dcbe56ffecded973142dc8e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2ad546b86dcbe56ffecded973142dc8e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.shimmer.sales.orders.create.items','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin::shimmer.sales.orders.create.items'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2ad546b86dcbe56ffecded973142dc8e)): ?>
<?php $attributes = $__attributesOriginal2ad546b86dcbe56ffecded973142dc8e; ?>
<?php unset($__attributesOriginal2ad546b86dcbe56ffecded973142dc8e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2ad546b86dcbe56ffecded973142dc8e)): ?>
<?php $component = $__componentOriginal2ad546b86dcbe56ffecded973142dc8e; ?>
<?php unset($__componentOriginal2ad546b86dcbe56ffecded973142dc8e); ?>
<?php endif; ?>

        <!-- Compare Items Shimmer Effect -->
        <?php if (isset($component)) { $__componentOriginal2ad546b86dcbe56ffecded973142dc8e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2ad546b86dcbe56ffecded973142dc8e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.shimmer.sales.orders.create.items','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin::shimmer.sales.orders.create.items'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2ad546b86dcbe56ffecded973142dc8e)): ?>
<?php $attributes = $__attributesOriginal2ad546b86dcbe56ffecded973142dc8e; ?>
<?php unset($__attributesOriginal2ad546b86dcbe56ffecded973142dc8e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2ad546b86dcbe56ffecded973142dc8e)): ?>
<?php $component = $__componentOriginal2ad546b86dcbe56ffecded973142dc8e; ?>
<?php unset($__componentOriginal2ad546b86dcbe56ffecded973142dc8e); ?>
<?php endif; ?>

        <!-- Recent Order Items Shimmer Effect -->
        <?php if (isset($component)) { $__componentOriginal2ad546b86dcbe56ffecded973142dc8e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2ad546b86dcbe56ffecded973142dc8e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.shimmer.sales.orders.create.items','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin::shimmer.sales.orders.create.items'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2ad546b86dcbe56ffecded973142dc8e)): ?>
<?php $attributes = $__attributesOriginal2ad546b86dcbe56ffecded973142dc8e; ?>
<?php unset($__attributesOriginal2ad546b86dcbe56ffecded973142dc8e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2ad546b86dcbe56ffecded973142dc8e)): ?>
<?php $component = $__componentOriginal2ad546b86dcbe56ffecded973142dc8e; ?>
<?php unset($__componentOriginal2ad546b86dcbe56ffecded973142dc8e); ?>
<?php endif; ?>
    </div>
</div><?php /**PATH /var/www/html/packages/Webkul/Admin/src/resources/views/components/shimmer/sales/orders/create.blade.php ENDPATH**/ ?>