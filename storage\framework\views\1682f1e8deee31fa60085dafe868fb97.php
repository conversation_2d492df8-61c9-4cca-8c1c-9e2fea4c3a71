<?php $__env->startSection('page_title'); ?>
    Quản lý hoạt chất
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="content">
        <div class="page-header">
            <div class="page-title">
                <h1><PERSON>h sách hoạt chất</h1>
            </div>
            <div class="page-action">
                <a href="<?php echo e(route('admin.catalog.active-ingredients.create')); ?>" class="btn btn-lg btn-primary">
                    Thêm hoạt chất mới
                </a>
            </div>
        </div>

        <div class="page-content">
            <?php echo $__env->make('admin::layouts.flash-messages', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Tên ho<PERSON>t chất</th>
                            <th><PERSON><PERSON> tả</th>
                            <th>Trạng thái</th>
                            <th>Ngày tạo</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $activeIngredients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ingredient): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td><?php echo e($ingredient->id); ?></td>
                                <td><?php echo e($ingredient->name); ?></td>
                                <td><?php echo e($ingredient->description); ?></td>
                                <td>
                                    <span class="badge badge-<?php echo e($ingredient->status ? 'success' : 'danger'); ?>">
                                        <?php echo e($ingredient->status ? 'Hoạt động' : 'Không hoạt động'); ?>

                                    </span>
                                </td>
                                <td><?php echo e($ingredient->created_at->format('d/m/Y H:i:s')); ?></td>
                                <td class="actions">
                                    <a href="<?php echo e(route('admin.catalog.active-ingredients.edit', $ingredient->id)); ?>" class="btn btn-sm btn-primary">
                                        <i class="icon pencil-lg-icon"></i>
                                    </a>
                                    <form action="<?php echo e(route('admin.catalog.active-ingredients.destroy', $ingredient->id)); ?>" 
                                          method="POST" 
                                          style="display: inline-block;"
                                          onsubmit="return confirm('Bạn có chắc chắn muốn xóa hoạt chất này?');">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="btn btn-sm btn-danger">
                                            <i class="icon trash-icon"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>

            <div class="pagination">
                <?php echo e($activeIngredients->links()); ?>

            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('admin::layouts.content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/html/resources/views/admin/catalog/active-ingredients/index.blade.php ENDPATH**/ ?>