<?php if (isset($component)) { $__componentOriginal8001c520f4b7dcb40a16cd3b411856d1 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.layouts.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin::layouts'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('title', null, []); ?> 
        Trả lời câu hỏi
     <?php $__env->endSlot(); ?>

    <div class="flex gap-4 justify-between items-center max-sm:flex-wrap">
        <p class="text-xl text-gray-800 dark:text-white font-bold">
            Trả lời câu hỏi
        </p>

        <div class="flex gap-x-2.5 items-center">
            <a
                href="<?php echo e(route('admin.catalog.product-questions.index')); ?>"
                class="transparent-button hover:bg-gray-200 dark:hover:bg-gray-800 dark:text-white"
            >
                Quay lại
            </a>
        </div>
    </div>

    <div class="box-shadow rounded bg-white dark:bg-gray-900 p-4">
        <form id="answer-form" method="POST" action="<?php echo e(route('admin.catalog.product-questions.update', $question->id)); ?>">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>

            <!-- Question Info -->
            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Sản phẩm
                        </label>
                        <p class="text-gray-900 dark:text-white"><?php echo e($productName); ?></p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Khách hàng
                        </label>
                        <p class="text-gray-900 dark:text-white"><?php echo e($customerName); ?></p>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Câu hỏi
                    </label>
                    <div class="p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                        <p class="text-gray-900 dark:text-white"><?php echo e($question->question); ?></p>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Ngày tạo
                    </label>
                    <p class="text-gray-600 dark:text-gray-400"><?php echo e($question->created_at->format('d/m/Y H:i')); ?></p>
                </div>
            </div>

            <!-- Existing Answer (if any) -->
            <?php if($question->answer): ?>
                <div class="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-md border border-blue-200 dark:border-blue-800">
                    <h3 class="text-sm font-medium text-blue-800 dark:text-blue-300 mb-2">Câu trả lời hiện tại</h3>
                    <p class="text-blue-700 dark:text-blue-200 mb-2"><?php echo e($question->answer->answer); ?></p>
                    <p class="text-xs text-blue-600 dark:text-blue-400">
                        Trả lời bởi: <?php echo e($question->answer->admin->name); ?> - <?php echo e($question->answer->created_at->format('d/m/Y H:i')); ?>

                    </p>
                </div>
            <?php endif; ?>

            <!-- Answer Form -->
            <div class="mb-6">
                <label for="answer" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <?php echo e($question->answer ? 'Cập nhật câu trả lời' : 'Câu trả lời'); ?> <span class="text-red-500">*</span>
                </label>
                <textarea
                    id="answer"
                    name="answer"
                    rows="6"
                    class="block w-full rounded-lg border border-gray-300 bg-gray-50 px-3 py-2 text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                    placeholder="Nhập câu trả lời..."
                    required
                ><?php echo e($question->answer ? $question->answer->answer : ''); ?></textarea>
            </div>



            <!-- Submit Buttons -->
            <div class="flex gap-2">
                <button
                    type="submit"
                    class="primary-button"
                >
                    <?php echo e($question->answer ? 'Cập nhật' : 'Trả lời'); ?>

                </button>

                <a
                    href="<?php echo e(route('admin.catalog.product-questions.index')); ?>"
                    class="secondary-button"
                >
                    Hủy
                </a>
            </div>
        </form>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1)): ?>
<?php $attributes = $__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1; ?>
<?php unset($__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8001c520f4b7dcb40a16cd3b411856d1)): ?>
<?php $component = $__componentOriginal8001c520f4b7dcb40a16cd3b411856d1; ?>
<?php unset($__componentOriginal8001c520f4b7dcb40a16cd3b411856d1); ?>
<?php endif; ?>
<?php /**PATH /var/www/html/packages/Webkul/Admin/src/resources/views/catalog/product-questions/edit.blade.php ENDPATH**/ ?>