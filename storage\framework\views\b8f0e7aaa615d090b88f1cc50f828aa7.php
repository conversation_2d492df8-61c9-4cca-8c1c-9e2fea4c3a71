<?php echo app('App\Services\MedicalViteService')->renderAssets(['resources/themes/medical/css/input-focus.css']); ?>
<form 
    class="quick-order-search-section" 
    action="<?php echo e($action ?? ''); ?>" 
    method="<?php echo e($method ?? 'GET'); ?>"
>
    <div class="quick-order-search-bar">
        <input 
            type="text" 
            name="<?php echo e($name ?? 'keyword'); ?>" 
            class="quick-order-search-input" 
            placeholder="<?php echo e($placeholder ?? 'Nhập tên thuốc, hoạt chất, công dụng...'); ?>"
            value="<?php echo e(request()->input($name ?? 'keyword', $value ?? '')); ?>"
        >
        <button type="submit" class="quick-order-search-btn">Tìm kiếm</button>
    </div>
</form>
<style>
.quick-order-search-bar {
    display: flex;
    gap: 8px;
    margin-top: 6px;
    margin-bottom: 45px;
}

.quick-order-search-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}
.quick-order-search-btn {
    background: #FF6B00;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 8px 18px;
    font-size: 1rem;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.15s;
}
.quick-order-search-btn:hover {
    background: #e65c00;
}
</style><?php /**PATH /var/www/html/app/Providers/../../resources/themes/medical/views/common/search_bar.blade.php ENDPATH**/ ?>