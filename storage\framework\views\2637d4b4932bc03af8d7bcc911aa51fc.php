<?php $__env->startSection('content'); ?>
<?php echo app('App\Services\MedicalViteService')->renderAssets(['resources/themes/medical/css/active_ingredient.css']); ?>

<div class="active-ingredient-container">
    <?php echo $__env->make('medical::common.search_bar', [
    'action' => route('active_ingredient'),
    'method' => 'GET',
    'name' => 'keyword',
    'placeholder' => 'Nhập tên hoạt chất...',
    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <div class="ingredient-list">
        <?php $__empty_1 = true; $__currentLoopData = $activeIngredients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ingredient): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="ingredient-card">
                <div class="ingredient-title"><?php echo e($ingredient->name); ?></div>
                <div class="ingredient-desc"><?php echo e(Str::limit($ingredient->description, 150)); ?></div>
                <button onclick="window.location.href='<?php echo e(route('ingredient_detail', $ingredient->slug)); ?>'" class="ingredient-btn">Xem chi tiết</button>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="no-ingredients">
                <p>Không tìm thấy hoạt chất nào.</p>
            </div>
        <?php endif; ?>
    </div>

    <div class="ingredient-pagination">
        <?php echo e($activeIngredients->links('vendor.pagination.custom')); ?>

    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('medical::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/html/app/Providers/../../resources/themes/medical/views/active_ingredient/active_ingredient.blade.php ENDPATH**/ ?>