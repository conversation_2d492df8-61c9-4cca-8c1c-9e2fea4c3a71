<?php $__env->startSection('page_title'); ?>
    <?php echo e(__('admin::app.catalog.brands.title')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="content">
        <div class="page-header">
            <div class="page-title">
                <h1><?php echo e(__('admin::app.catalog.brands.title')); ?></h1>
            </div>
            <div class="page-action">
                <a href="<?php echo e(route('admin.catalog.brands.index')); ?>" class="btn btn-lg btn-primary">
                    <?php echo e(__('admin::app.catalog.brands.back-to-list')); ?>

                </a>
            </div>
        </div>

        <div class="page-content">
            <div class="form-group">
                <label><?php echo e(__('admin::app.catalog.brands.brand-name')); ?>:</label>
                <p><?php echo e($brand->brand_name); ?></p>
            </div>
            <div class="form-group">
                <label><?php echo e(__('admin::app.catalog.brands.brand-image')); ?>:</label>
                <?php if($brand->brand_image): ?>
                    <img src="<?php echo e(asset('storage/' . $brand->brand_image)); ?>" alt="<?php echo e($brand->brand_name); ?>" width="120" />
                <?php else: ?>
                    <p>N/A</p>
                <?php endif; ?>
            </div>
            <div class="form-group">
                <label><?php echo e(__('admin::app.catalog.brands.brand-description')); ?>:</label>
                <p><?php echo e($brand->brand_description); ?></p>
            </div>
            <div class="form-group">
                <label><?php echo e(__('admin::app.catalog.brands.attribute-option-id')); ?>:</label>
                <p><?php echo e($brand->attribute_option_id); ?></p>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('admin::layouts.content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/html/packages/Webkul/Admin/src/resources/views/catalog/brands/show.blade.php ENDPATH**/ ?>