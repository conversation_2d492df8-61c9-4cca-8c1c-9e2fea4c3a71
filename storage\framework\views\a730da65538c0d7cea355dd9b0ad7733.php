<?php if (isset($component)) { $__componentOriginal8001c520f4b7dcb40a16cd3b411856d1 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.layouts.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin::layouts'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('title', null, []); ?> 
        Quản lý hoạt chất
     <?php $__env->endSlot(); ?>

    <div class="flex items-center justify-between gap-4 max-sm:flex-wrap">
        <!-- Title -->
        <p class="text-xl font-bold text-gray-800 dark:text-white">
            Danh sách hoạt chất
        </p>

        <div class="flex items-center gap-x-2.5">
            <a href="<?php echo e(route('admin.catalog.active-ingredients.create')); ?>">
                <div class="primary-button">
                    Thêm hoạt chất mới
                </div>
            </a>
        </div>
    </div>

    <div class="mt-4 bg-white dark:bg-gray-900 rounded box-shadow">
        <div class="p-4">
            <?php if(count($activeIngredients) > 0): ?>
                <div class="overflow-x-auto">
                    <table class="w-full text-sm text-left">
                        <thead class="bg-gray-50 dark:bg-gray-800 text-gray-600 dark:text-gray-300">
                            <tr>
                                <th class="px-4 py-3">ID</th>
                                <th class="px-4 py-3">Tên hoạt chất</th>
                                <th class="px-4 py-3">Mô tả</th>
                                <th class="px-4 py-3">Trạng thái</th>
                                <th class="px-4 py-3">Ngày tạo</th>
                                <th class="px-4 py-3">Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $activeIngredients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ingredient): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr class="border-b dark:border-gray-800">
                                    <td class="px-4 py-3"><?php echo e($ingredient->id); ?></td>
                                    <td class="px-4 py-3"><?php echo e($ingredient->name); ?></td>
                                    <td class="px-4 py-3"><?php echo e(Str::limit($ingredient->description, 100)); ?></td>
                                    <td class="px-4 py-3">
                                        <span class="px-2 py-1 rounded <?php echo e($ingredient->status ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'); ?>">
                                            <?php echo e($ingredient->status ? 'Hoạt động' : 'Không hoạt động'); ?>

                                        </span>
                                    </td>
                                    <td class="px-4 py-3"><?php echo e($ingredient->created_at->format('d/m/Y H:i:s')); ?></td>
                                    <td class="px-4 py-3">
                                        <div class="flex gap-1">
                                            <a
                                                href="<?php echo e(route('admin.catalog.active-ingredients.edit', $ingredient->id)); ?>"
                                                class="p-1.5 rounded-md text-gray-600 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-800"
                                            >
                                                <span class="icon-edit text-lg"></span>
                                            </a>

                                            <form
                                                action="<?php echo e(route('admin.catalog.active-ingredients.destroy', $ingredient->id)); ?>"
                                                method="POST"
                                                onsubmit="return confirm('Bạn có chắc chắn muốn xóa hoạt chất này?');"
                                            >
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>

                                                <button
                                                    type="submit"
                                                    class="p-1.5 rounded-md text-gray-600 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-800"
                                                >
                                                    <span class="icon-delete text-lg"></span>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <div class="mt-4">
                    <?php echo e($activeIngredients->links()); ?>

                </div>
            <?php else: ?>
                <p class="text-center py-4">Không có hoạt chất nào.</p>
            <?php endif; ?>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1)): ?>
<?php $attributes = $__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1; ?>
<?php unset($__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8001c520f4b7dcb40a16cd3b411856d1)): ?>
<?php $component = $__componentOriginal8001c520f4b7dcb40a16cd3b411856d1; ?>
<?php unset($__componentOriginal8001c520f4b7dcb40a16cd3b411856d1); ?>
<?php endif; ?>
<?php /**PATH /var/www/html/packages/Webkul/Admin/src/resources/views/catalog/active-ingredients/index.blade.php ENDPATH**/ ?>