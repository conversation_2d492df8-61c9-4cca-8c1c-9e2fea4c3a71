<?php if (isset($component)) { $__componentOriginal8001c520f4b7dcb40a16cd3b411856d1 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.layouts.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin::layouts'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('title', null, []); ?> 
        Quản lý Hỏi & Đáp Sản phẩm
     <?php $__env->endSlot(); ?>

    <div class="flex gap-4 justify-between items-center max-sm:flex-wrap">
        <p class="text-xl text-gray-800 dark:text-white font-bold">
            Quản lý Hỏi & Đáp Sản phẩm
        </p>
    </div>

    <div class="box-shadow rounded bg-white dark:bg-gray-900" id="product-questions-app">
        <!-- Toolbar -->
        <div class="flex items-center justify-between gap-4 border-b border-gray-200 p-4 text-sm dark:border-gray-800">
            <!-- Search -->
            <div class="flex items-center gap-x-1">
                <div class="relative">
                    <input
                        type="text"
                        id="search-input"
                        class="block w-full rounded-lg border border-gray-300 bg-gray-50 px-2.5 py-1.5 text-xs text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                        placeholder="Tìm kiếm..."
                        style="width: 200px;"
                    />
                    <button
                        type="button"
                        onclick="performSearch()"
                        class="absolute inset-y-0 right-0 flex items-center pr-3"
                    >
                        <span class="icon-search text-lg text-gray-500 dark:text-gray-400"></span>
                    </button>
                </div>

                <span id="search-results" class="text-xs text-gray-600 dark:text-gray-300"></span>
            </div>

            <!-- Filters and Actions -->
            <div class="flex items-center gap-x-2">


                <!-- Answer Filter -->
                <select
                    id="answer-filter"
                    onchange="applyFilters()"
                    class="block rounded-lg border border-gray-300 bg-gray-50 px-2.5 py-1.5 text-xs text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-blue-500 dark:focus:ring-blue-500"
                >
                    <option value="">Tất cả</option>
                    <option value="answered">Đã trả lời</option>
                    <option value="unanswered">Chưa trả lời</option>
                </select>

                <!-- Per Page -->
                <div class="flex items-center gap-x-1">
                    <select
                        id="per-page-select"
                        onchange="changePerPage()"
                        class="block rounded-lg border border-gray-300 bg-gray-50 px-2.5 py-1.5 text-xs text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-blue-500 dark:focus:ring-blue-500"
                    >
                        <option value="10">10</option>
                        <option value="20">20</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                    <span class="text-xs text-gray-600 dark:text-gray-300">Per Page</span>
                </div>

                <!-- Pagination Info -->
                <span id="pagination-info" class="text-xs text-gray-600 dark:text-gray-300">
                    <!-- Will be updated by JavaScript -->
                </span>

                <!-- Navigation Buttons -->
                <div class="flex items-center gap-x-1">
                    <button
                        id="prev-button"
                        onclick="goToPreviousPage()"
                        class="inline-flex cursor-pointer appearance-none items-center justify-center rounded-md border border-gray-300 bg-white p-1.5 text-gray-600 transition-all hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
                        title="Previous"
                    >
                        <span class="icon-sort-left text-lg"></span>
                    </button>

                    <button
                        id="next-button"
                        onclick="goToNextPage()"
                        class="inline-flex cursor-pointer appearance-none items-center justify-center rounded-md border border-gray-300 bg-white p-1.5 text-gray-600 transition-all hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
                        title="Next"
                    >
                        <span class="icon-sort-right text-lg"></span>
                    </button>
                </div>

                <!-- Filter Button -->
                <button
                    type="button"
                    onclick="toggleFilters()"
                    class="inline-flex cursor-pointer appearance-none items-center justify-center gap-x-1 rounded-md border border-gray-300 bg-white px-2.5 py-1.5 text-center text-xs font-semibold text-gray-600 transition-all hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
                >
                    <span class="icon-filter text-lg"></span>
                    Filter
                </button>
            </div>
        </div>

        <div class="table-responsive grid w-full overflow-hidden rounded">
            <?php if (isset($component)) { $__componentOriginala9dad9f471f1e8ff345be80579eb8136 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala9dad9f471f1e8ff345be80579eb8136 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.table.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin::table'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                <?php if (isset($component)) { $__componentOriginal8ee89c0b398bd7314c2e7815b044fc82 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8ee89c0b398bd7314c2e7815b044fc82 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.table.thead.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin::table.thead'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                    <?php if (isset($component)) { $__componentOriginal95a122c91c33f6d66a15a82d7ca67172 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal95a122c91c33f6d66a15a82d7ca67172 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.table.thead.tr','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin::table.thead.tr'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                        <?php if (isset($component)) { $__componentOriginal2b66f2da706603ab43da37c4a360ae32 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2b66f2da706603ab43da37c4a360ae32 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.table.th','data' => ['class' => '!p-0']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin::table.th'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => '!p-0']); ?>
                            <input
                                type="checkbox"
                                class="hidden"
                                id="select_all"
                            />
                            <label
                                class="icon-uncheckbox cursor-pointer rounded-md p-1.5 text-2xl"
                                for="select_all"
                            ></label>
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2b66f2da706603ab43da37c4a360ae32)): ?>
<?php $attributes = $__attributesOriginal2b66f2da706603ab43da37c4a360ae32; ?>
<?php unset($__attributesOriginal2b66f2da706603ab43da37c4a360ae32); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2b66f2da706603ab43da37c4a360ae32)): ?>
<?php $component = $__componentOriginal2b66f2da706603ab43da37c4a360ae32; ?>
<?php unset($__componentOriginal2b66f2da706603ab43da37c4a360ae32); ?>
<?php endif; ?>

                        <?php if (isset($component)) { $__componentOriginal2b66f2da706603ab43da37c4a360ae32 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2b66f2da706603ab43da37c4a360ae32 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.table.th','data' => ['class' => '!p-0']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin::table.th'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => '!p-0']); ?>
                            ID
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2b66f2da706603ab43da37c4a360ae32)): ?>
<?php $attributes = $__attributesOriginal2b66f2da706603ab43da37c4a360ae32; ?>
<?php unset($__attributesOriginal2b66f2da706603ab43da37c4a360ae32); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2b66f2da706603ab43da37c4a360ae32)): ?>
<?php $component = $__componentOriginal2b66f2da706603ab43da37c4a360ae32; ?>
<?php unset($__componentOriginal2b66f2da706603ab43da37c4a360ae32); ?>
<?php endif; ?>

                        <?php if (isset($component)) { $__componentOriginal2b66f2da706603ab43da37c4a360ae32 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2b66f2da706603ab43da37c4a360ae32 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.table.th','data' => ['class' => '!p-0']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin::table.th'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => '!p-0']); ?>
                            Sản phẩm
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2b66f2da706603ab43da37c4a360ae32)): ?>
<?php $attributes = $__attributesOriginal2b66f2da706603ab43da37c4a360ae32; ?>
<?php unset($__attributesOriginal2b66f2da706603ab43da37c4a360ae32); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2b66f2da706603ab43da37c4a360ae32)): ?>
<?php $component = $__componentOriginal2b66f2da706603ab43da37c4a360ae32; ?>
<?php unset($__componentOriginal2b66f2da706603ab43da37c4a360ae32); ?>
<?php endif; ?>

                        <?php if (isset($component)) { $__componentOriginal2b66f2da706603ab43da37c4a360ae32 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2b66f2da706603ab43da37c4a360ae32 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.table.th','data' => ['class' => '!p-0']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin::table.th'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => '!p-0']); ?>
                            Khách hàng
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2b66f2da706603ab43da37c4a360ae32)): ?>
<?php $attributes = $__attributesOriginal2b66f2da706603ab43da37c4a360ae32; ?>
<?php unset($__attributesOriginal2b66f2da706603ab43da37c4a360ae32); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2b66f2da706603ab43da37c4a360ae32)): ?>
<?php $component = $__componentOriginal2b66f2da706603ab43da37c4a360ae32; ?>
<?php unset($__componentOriginal2b66f2da706603ab43da37c4a360ae32); ?>
<?php endif; ?>

                        <?php if (isset($component)) { $__componentOriginal2b66f2da706603ab43da37c4a360ae32 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2b66f2da706603ab43da37c4a360ae32 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.table.th','data' => ['class' => '!p-0']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin::table.th'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => '!p-0']); ?>
                            Câu hỏi
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2b66f2da706603ab43da37c4a360ae32)): ?>
<?php $attributes = $__attributesOriginal2b66f2da706603ab43da37c4a360ae32; ?>
<?php unset($__attributesOriginal2b66f2da706603ab43da37c4a360ae32); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2b66f2da706603ab43da37c4a360ae32)): ?>
<?php $component = $__componentOriginal2b66f2da706603ab43da37c4a360ae32; ?>
<?php unset($__componentOriginal2b66f2da706603ab43da37c4a360ae32); ?>
<?php endif; ?>

                        <?php if (isset($component)) { $__componentOriginal2b66f2da706603ab43da37c4a360ae32 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2b66f2da706603ab43da37c4a360ae32 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.table.th','data' => ['class' => '!p-0']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin::table.th'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => '!p-0']); ?>
                            Trả lời
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2b66f2da706603ab43da37c4a360ae32)): ?>
<?php $attributes = $__attributesOriginal2b66f2da706603ab43da37c4a360ae32; ?>
<?php unset($__attributesOriginal2b66f2da706603ab43da37c4a360ae32); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2b66f2da706603ab43da37c4a360ae32)): ?>
<?php $component = $__componentOriginal2b66f2da706603ab43da37c4a360ae32; ?>
<?php unset($__componentOriginal2b66f2da706603ab43da37c4a360ae32); ?>
<?php endif; ?>

                        <?php if (isset($component)) { $__componentOriginal2b66f2da706603ab43da37c4a360ae32 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2b66f2da706603ab43da37c4a360ae32 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.table.th','data' => ['class' => '!p-0']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin::table.th'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => '!p-0']); ?>
                            Ngày tạo
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2b66f2da706603ab43da37c4a360ae32)): ?>
<?php $attributes = $__attributesOriginal2b66f2da706603ab43da37c4a360ae32; ?>
<?php unset($__attributesOriginal2b66f2da706603ab43da37c4a360ae32); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2b66f2da706603ab43da37c4a360ae32)): ?>
<?php $component = $__componentOriginal2b66f2da706603ab43da37c4a360ae32; ?>
<?php unset($__componentOriginal2b66f2da706603ab43da37c4a360ae32); ?>
<?php endif; ?>

                        <?php if (isset($component)) { $__componentOriginal2b66f2da706603ab43da37c4a360ae32 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2b66f2da706603ab43da37c4a360ae32 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.table.th','data' => ['class' => '!p-0']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin::table.th'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => '!p-0']); ?>
                            Hành động
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2b66f2da706603ab43da37c4a360ae32)): ?>
<?php $attributes = $__attributesOriginal2b66f2da706603ab43da37c4a360ae32; ?>
<?php unset($__attributesOriginal2b66f2da706603ab43da37c4a360ae32); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2b66f2da706603ab43da37c4a360ae32)): ?>
<?php $component = $__componentOriginal2b66f2da706603ab43da37c4a360ae32; ?>
<?php unset($__componentOriginal2b66f2da706603ab43da37c4a360ae32); ?>
<?php endif; ?>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal95a122c91c33f6d66a15a82d7ca67172)): ?>
<?php $attributes = $__attributesOriginal95a122c91c33f6d66a15a82d7ca67172; ?>
<?php unset($__attributesOriginal95a122c91c33f6d66a15a82d7ca67172); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal95a122c91c33f6d66a15a82d7ca67172)): ?>
<?php $component = $__componentOriginal95a122c91c33f6d66a15a82d7ca67172; ?>
<?php unset($__componentOriginal95a122c91c33f6d66a15a82d7ca67172); ?>
<?php endif; ?>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8ee89c0b398bd7314c2e7815b044fc82)): ?>
<?php $attributes = $__attributesOriginal8ee89c0b398bd7314c2e7815b044fc82; ?>
<?php unset($__attributesOriginal8ee89c0b398bd7314c2e7815b044fc82); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8ee89c0b398bd7314c2e7815b044fc82)): ?>
<?php $component = $__componentOriginal8ee89c0b398bd7314c2e7815b044fc82; ?>
<?php unset($__componentOriginal8ee89c0b398bd7314c2e7815b044fc82); ?>
<?php endif; ?>

                <?php if (isset($component)) { $__componentOriginalde01fbd71b7145d08385ea395943e136 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalde01fbd71b7145d08385ea395943e136 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.table.tbody.index','data' => ['id' => 'questions-tbody']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin::table.tbody'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'questions-tbody']); ?>
                    <!-- Data will be loaded here -->
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalde01fbd71b7145d08385ea395943e136)): ?>
<?php $attributes = $__attributesOriginalde01fbd71b7145d08385ea395943e136; ?>
<?php unset($__attributesOriginalde01fbd71b7145d08385ea395943e136); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalde01fbd71b7145d08385ea395943e136)): ?>
<?php $component = $__componentOriginalde01fbd71b7145d08385ea395943e136; ?>
<?php unset($__componentOriginalde01fbd71b7145d08385ea395943e136); ?>
<?php endif; ?>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala9dad9f471f1e8ff345be80579eb8136)): ?>
<?php $attributes = $__attributesOriginala9dad9f471f1e8ff345be80579eb8136; ?>
<?php unset($__attributesOriginala9dad9f471f1e8ff345be80579eb8136); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala9dad9f471f1e8ff345be80579eb8136)): ?>
<?php $component = $__componentOriginala9dad9f471f1e8ff345be80579eb8136; ?>
<?php unset($__componentOriginala9dad9f471f1e8ff345be80579eb8136); ?>
<?php endif; ?>
        </div>


    </div>

    <style>
        .label-success {
            @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300;
        }

        .label-pending {
            @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300;
        }

        .label-danger {
            @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300;
        }

        .label-info {
            @apply bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300;
        }
    </style>

    <script>
        let currentFilters = {
            search: '',
            answer: '',
            perPage: 10
        };

        let currentPagination = {
            current_page: 1,
            last_page: 1
        };

        // Load questions on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadQuestions();

            // Add enter key support for search
            document.getElementById('search-input').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });
        });

        function loadQuestions(page = 1) {
            const params = new URLSearchParams({
                page: page,
                per_page: currentFilters.perPage,
                search: currentFilters.search,
                answer: currentFilters.answer
            });

            fetch(`<?php echo e(route('admin.catalog.product-questions.index')); ?>?${params}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                renderQuestions(data.records);
                updatePaginationInfo(data.pagination);
                updateSearchResults(data.pagination.total);
                currentPagination = data.pagination;
            })
            .catch(error => {
                console.error('Error loading questions:', error);
            });
        }

        function performSearch() {
            currentFilters.search = document.getElementById('search-input').value;
            loadQuestions(1);
        }

        function applyFilters() {
            currentFilters.answer = document.getElementById('answer-filter').value;
            loadQuestions(1);
        }

        function changePerPage() {
            currentFilters.perPage = document.getElementById('per-page-select').value;
            loadQuestions(1);
        }

        function toggleFilters() {
            // Reset all filters
            currentFilters = {
                search: '',
                answer: '',
                perPage: 10
            };

            document.getElementById('search-input').value = '';
            document.getElementById('answer-filter').value = '';
            document.getElementById('per-page-select').value = '10';

            loadQuestions(1);
        }

        function updateSearchResults(total) {
            const searchResults = document.getElementById('search-results');
            if (currentFilters.search || currentFilters.answer) {
                searchResults.textContent = `${total} Results`;
            } else {
                searchResults.textContent = '';
            }
        }

        function updatePaginationInfo(pagination) {
            const paginationInfo = document.getElementById('pagination-info');
            paginationInfo.textContent = `${pagination.current_page} of ${pagination.last_page}`;

            // Update navigation buttons
            const prevButton = document.getElementById('prev-button');
            const nextButton = document.getElementById('next-button');

            if (pagination.current_page <= 1) {
                prevButton.disabled = true;
                prevButton.classList.add('cursor-not-allowed', 'opacity-50');
                prevButton.classList.remove('cursor-pointer', 'hover:bg-gray-50');
            } else {
                prevButton.disabled = false;
                prevButton.classList.remove('cursor-not-allowed', 'opacity-50');
                prevButton.classList.add('cursor-pointer', 'hover:bg-gray-50');
            }

            if (pagination.current_page >= pagination.last_page) {
                nextButton.disabled = true;
                nextButton.classList.add('cursor-not-allowed', 'opacity-50');
                nextButton.classList.remove('cursor-pointer', 'hover:bg-gray-50');
            } else {
                nextButton.disabled = false;
                nextButton.classList.remove('cursor-not-allowed', 'opacity-50');
                nextButton.classList.add('cursor-pointer', 'hover:bg-gray-50');
            }
        }

        function goToPreviousPage() {
            if (currentPagination.current_page > 1) {
                loadQuestions(currentPagination.current_page - 1);
            }
        }

        function goToNextPage() {
            if (currentPagination.current_page < currentPagination.last_page) {
                loadQuestions(currentPagination.current_page + 1);
            }
        }

        function renderQuestions(questions) {
            const tbody = document.getElementById('questions-tbody');
            tbody.innerHTML = '';

            questions.forEach(question => {
                const row = document.createElement('tr');
                row.className = 'border-b border-slate-200 transition-all hover:bg-gray-50 dark:border-gray-800 dark:hover:bg-gray-950';

                row.innerHTML = `
                    <td class="select-none">
                        <input
                            type="checkbox"
                            class="hidden peer"
                            id="checkbox_${question.id}"
                        />
                        <label
                            class="icon-uncheckbox cursor-pointer rounded-md p-1.5 text-2xl peer-checked:icon-checked peer-checked:text-blue-600"
                            for="checkbox_${question.id}"
                        ></label>
                    </td>

                    <td class="px-4 py-4 text-gray-600 dark:text-gray-300">
                        ${question.id}
                    </td>

                    <td class="px-4 py-4 text-gray-600 dark:text-gray-300">
                        ${question.product_name}
                    </td>

                    <td class="px-4 py-4 text-gray-600 dark:text-gray-300">
                        ${question.customer_name}
                    </td>

                    <td class="px-4 py-4 text-gray-600 dark:text-gray-300">
                        <div class="max-w-xs truncate" title="${question.question}">
                            ${question.question}
                        </div>
                    </td>

                    <td class="px-4 py-4">
                        <span class="label-${question.has_answer === 'Đã trả lời' ? 'success' : 'pending'} mx-1.5 rounded px-2 py-1 text-xs font-semibold">
                            ${question.has_answer}
                        </span>
                    </td>

                    <td class="px-4 py-4 text-gray-600 dark:text-gray-300">
                        ${question.created_at}
                    </td>

                    <td class="px-4 py-4">
                        <div class="flex gap-1.5">
                            <a
                                href="#"
                                onclick="editQuestion(${question.id}); return false;"
                                class="icon-edit cursor-pointer rounded-md p-1.5 text-2xl transition-all hover:bg-gray-200 dark:hover:bg-gray-800"
                                title="Trả lời"
                            ></a>

                            <a
                                href="#"
                                onclick="deleteQuestion(${question.id}); return false;"
                                class="icon-delete cursor-pointer rounded-md p-1.5 text-2xl transition-all hover:bg-gray-200 dark:hover:bg-gray-800"
                                title="Xóa"
                            ></a>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }



        function getStatusColor(status) {
            switch(status) {
                case 'approved': return 'success';
                case 'pending': return 'pending';
                case 'rejected': return 'danger';
                default: return 'info';
            }
        }

        function getStatusText(status) {
            switch(status) {
                case 'approved': return 'Đã duyệt';
                case 'pending': return 'Chờ duyệt';
                case 'rejected': return 'Từ chối';
                default: return status;
            }
        }

        function editQuestion(id) {
            // Redirect to edit page
            window.location.href = `<?php echo e(route('admin.catalog.product-questions.index')); ?>/${id}/edit`;
        }

        function deleteQuestion(id) {
            if (!confirm('Bạn có chắc chắn muốn xóa câu hỏi này?')) {
                return;
            }

            fetch(`<?php echo e(route('admin.catalog.product-questions.index')); ?>/${id}`, {
                method: 'DELETE',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    loadQuestions();
                } else {
                    alert(data.message);
                }
            })
            .catch(error => {
                console.error('Error deleting question:', error);
                alert('Có lỗi xảy ra khi xóa câu hỏi');
            });
        }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1)): ?>
<?php $attributes = $__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1; ?>
<?php unset($__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8001c520f4b7dcb40a16cd3b411856d1)): ?>
<?php $component = $__componentOriginal8001c520f4b7dcb40a16cd3b411856d1; ?>
<?php unset($__componentOriginal8001c520f4b7dcb40a16cd3b411856d1); ?>
<?php endif; ?>
<?php /**PATH /var/www/html/packages/Webkul/Admin/src/resources/views/catalog/product-questions/index.blade.php ENDPATH**/ ?>